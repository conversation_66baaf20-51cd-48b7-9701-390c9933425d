from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain_google_genai import ChatGoogleGenerativeAI
from dotenv import load_dotenv, find_dotenv

load_dotenv(find_dotenv())

import asyncio


async def main():
    client = MultiServerMCPClient(
        {
            "math":{"command":"python", "args":["MathServer.py"], "transport":"stdio"},
            "weather":{"url":"http://localhost:8000/mcp","transport":"streamable-http"}

        }
    )

    tools = await client.get_tools()
    llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0.2)
    agent = create_react_agent(llm, tools)
    response = await agent.ainvoke("messages":var = [{"role": "user", "content": "What is 2 + 2?"}]
    })


asyncio.run(main())

